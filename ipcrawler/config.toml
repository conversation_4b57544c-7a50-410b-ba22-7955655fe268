# ipcrawler Configuration File
# ===========================
# This file allows you to set default values for commonly used options.
# All settings here can be overridden by command-line arguments.
# 
# NOTE: Wordlist management is handled automatically by SecLists detection.
# Only configure scan behavior and plugin settings here.

# Basic Scan Configuration
# ------------------------
# Uncomment and modify any settings you want to change from defaults:

# verbose = 1                    # Verbosity level (0-3)
# max-scans = 50                 # Maximum concurrent scans  
timeout = 120                   # Global timeout in minutes (2 hours)
target-timeout = 60             # Per-target timeout in minutes (1 hour)
# heartbeat = 60                 # Status update interval in seconds
# nmap-append = '-T3'            # Additional nmap arguments
# output = 'results'             # Results output directory

# Plugin Control
# --------------
# tags = 'default,safe'          # Only run plugins with these tags
# exclude-tags = 'slow,bruteforce' # Skip plugins with these tags

# Port Scanning Options
# ---------------------
enable-udp-scan = false           # Enable/disable UDP port scanning (can be slow)

# Web Enumeration Options
# -----------------------
# auto-hostname-discovery = true   # Automatically discover hostnames for better web enumeration
# vhost-enumeration-for-ips = true # Enable vhost enumeration for IP targets with discovered hostnames
# resilient-mode = true            # Use more reliable/safer default settings for better compatibility

# Smart Wordlist Selection
# ------------------------
smart-wordlists = true             # Enable intelligent technology-based wordlist selection
smart-wordlists-confidence = 0.7   # Technology detection confidence threshold (0.0-1.0)

# Network Settings  
# ----------------
# proxychains = true             # Run scans through proxychains

# Accessibility
# -------------
# accessible = true              # Screen reader friendly output

# Plugin-Specific Configuration
# -----------------------------
# Configure individual plugins (uncomment sections as needed):

[dirbuster]
threads = 20                   # Directory enumeration threads (default: 20)
timeout = 7200                 # Directory busting timeout (2 hours)

# [whatweb]
# aggression = '1'               # WhatWeb aggression level (1-4, default: 1 for reliability)
# ignore-errors = true           # Continue even with HTTP errors (default: true)

[nikto]
timeout = 3600                  # Nikto scan timeout (1 hour)

# [nmap-http]
# scripts = 'http-title,http-headers'  # Custom nmap scripts
